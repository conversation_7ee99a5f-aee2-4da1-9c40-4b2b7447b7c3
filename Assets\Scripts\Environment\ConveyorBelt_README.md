# Система Конвейерных Лент для Photon Fusion

## Описание
Система конвейерных лент для сетевого проекта на Photon Fusion, которая позволяет перемещать предметы с Rigidbody и NetworkRigidbody3D.

## Компоненты

### 1. ConveyorBelt.cs
Основной скрипт конвейерной ленты.

**Основные функции:**
- Перемещение предметов с Rigidbody/NetworkRigidbody3D
- Сетевая синхронизация состояния и скорости
- Анимация текстуры ленты
- Звуковые эффекты
- Визуализация направления в редакторе

**Настройки:**
- `conveyorDirection` - направление движения ленты
- `conveyorSpeed` - скорость движения (м/с)
- `affectedLayers` - слои объектов, которые будут перемещаться
- `forceMultiplier` - множитель силы
- `useVelocityMode` - режим скорости vs режим силы
- `maxVelocity` - максимальная скорость объектов

### 2. ConveyorController.cs
Контроллер для управления несколькими конвейерами.

**Функции:**
- Управление через UI (кнопки, слайдеры)
- Управление через триггеры
- Автоматическое переключение по таймеру
- Управление с клавиатуры

### 3. ConveyorItem.cs
Предмет, который может взаимодействовать с конвейерами.

**Особенности:**
- Наследуется от PickupItem
- Настраиваемое сопротивление конвейеру
- Эффекты движения и звуки
- Кастомные физические свойства

## Установка и Настройка

### Шаг 1: Создание конвейера
1. Создайте GameObject для конвейера
2. Добавьте компонент `ConveyorBelt`
3. Добавьте Collider (обязательно с isTrigger = true)
4. Настройте параметры в инспекторе

### Шаг 2: Настройка материала (опционально)
1. Создайте материал для ленты
2. Назначьте его в поле `beltMaterial`
3. Укажите название свойства текстуры в `texturePropertyName`

### Шаг 3: Настройка звука (опционально)
1. Добавьте AudioSource к объекту конвейера
2. Назначьте звуковой клип в `conveyorSound`
3. Включите `playSound`

### Шаг 4: Создание контроллера (опционально)
1. Создайте GameObject для контроллера
2. Добавьте компонент `ConveyorController`
3. Назначьте конвейеры в массив `conveyorBelts`
4. Настройте UI элементы если нужно

## Использование

### Программное управление
```csharp
// Получение компонента конвейера
ConveyorBelt conveyor = GetComponent<ConveyorBelt>();

// Управление состоянием
conveyor.SetConveyorActive(true);
conveyor.SetConveyorSpeed(10f);
conveyor.SetConveyorDirection(Vector3.forward);

// Получение информации
int objectsCount = conveyor.GetObjectsOnBeltCount();
bool isObjectOnBelt = conveyor.IsObjectOnBelt(rigidbody);
```

### Управление через контроллер
```csharp
ConveyorController controller = GetComponent<ConveyorController>();

// Управление всей системой
controller.ActivateConveyors();
controller.DeactivateConveyors();
controller.SetSpeed(5f);

// Добавление/удаление конвейеров
controller.AddConveyor(newConveyor);
controller.RemoveConveyor(oldConveyor);
```

### RPC методы для сетевого управления
```csharp
// Для ConveyorBelt
conveyor.RPC_SetActive(true);
conveyor.RPC_SetSpeed(8f);
conveyor.RPC_SetDirection(Vector3.right);

// Для ConveyorController
// Используйте публичные методы - они автоматически вызывают RPC
```

## Настройка предметов

### Для существующих предметов
Добавьте слой предмета в `affectedLayers` конвейера.

### Для специальных предметов
1. Наследуйте от `ConveyorItem` вместо `PickupItem`
2. Настройте параметры взаимодействия с конвейером
3. Добавьте эффекты и звуки

## Слои и физика

### Рекомендуемые слои:
- `Default` - обычные предметы
- `Items` - игровые предметы
- `Physics` - физические объекты

### Настройка физики:
1. Убедитесь, что у предметов есть Rigidbody
2. Для сетевых предметов добавьте NetworkRigidbody3D
3. Настройте массу, сопротивление и другие параметры

## Производительность

### Оптимизация:
- Используйте LayerMask для фильтрации объектов
- Ограничивайте количество объектов на конвейере
- Используйте Object Pooling для часто создаваемых предметов
- Отключайте неиспользуемые конвейеры

### Сетевая оптимизация:
- Только сервер обрабатывает физику конвейера
- Клиенты получают только результаты
- Используйте RPC только для управления, не для постоянных обновлений

## Отладка

### Визуальная отладка:
- Gizmos показывают направление конвейера в редакторе
- ConveyorItem показывает состояние на конвейере

### Методы отладки:
```csharp
// Проверка состояния
Debug.Log($"Objects on belt: {conveyor.GetObjectsOnBeltCount()}");
Debug.Log($"Conveyor active: {conveyor.IsActive}");
Debug.Log($"Conveyor speed: {conveyor.NetworkSpeed}");
```

## Примеры использования

### Простой конвейер
```csharp
// Создание и настройка базового конвейера
var conveyor = gameObject.AddComponent<ConveyorBelt>();
conveyor.conveyorDirection = Vector3.forward;
conveyor.conveyorSpeed = 5f;
```

### Система сортировки
```csharp
// Контроллер с несколькими конвейерами для сортировки предметов
var controller = gameObject.AddComponent<ConveyorController>();
controller.conveyorBelts = new ConveyorBelt[] { belt1, belt2, belt3 };
controller.autoToggleInterval = 10f; // переключение каждые 10 секунд
```

### Интерактивный конвейер
```csharp
// Конвейер, активируемый игроком
var controller = gameObject.AddComponent<ConveyorController>();
controller.useTrigger = true;
controller.requirePlayerToActivate = true;
controller.activeDuration = 30f; // работает 30 секунд после активации
```

## Требования
- Unity 2021.3 или выше
- Photon Fusion 2.0 или выше
- Компоненты: Rigidbody, Collider, NetworkObject
