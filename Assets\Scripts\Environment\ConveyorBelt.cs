using Fusion;
using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Конвейерная лента для перемещения предметов с Rigidbody и NetworkRigidbody3D в сетевом проекте Photon Fusion
/// </summary>
[RequireComponent(typeof(Collider))]
public class ConveyorBelt : NetworkBehaviour {
    
    [Header("Conveyor Settings")]
    [SerializeField] private Vector3 conveyorDirection = Vector3.forward;
    [SerializeField] private float conveyorSpeed = 5f;
    [SerializeField] private LayerMask affectedLayers = -1;
    
    [Header("Physics Settings")]
    [SerializeField] private float forceMultiplier = 1f;
    [SerializeField] private bool useVelocityMode = true;
    [SerializeField] private float maxVelocity = 10f;
    
    [Header("Visual Settings")]
    [SerializeField] private Material beltMaterial;
    [SerializeField] private float textureScrollSpeed = 1f;
    [SerializeField] private string texturePropertyName = "_MainTex";
    
    [Header("Audio")]
    [SerializeField] private AudioSource audioSource;
    [SerializeField] private AudioClip conveyorSound;
    [SerializeField] private bool playSound = true;
    
    // Список объектов на конвейере
    private HashSet<Rigidbody> objectsOnBelt = new HashSet<Rigidbody>();
    private Collider beltCollider;
    private Renderer beltRenderer;
    private Vector2 textureOffset;
    
    // Сетевые переменные
    [Networked] public bool IsActive { get; set; } = true;
    [Networked] public float NetworkSpeed { get; set; }
    
    public override void Spawned() {
        beltCollider = GetComponent<Collider>();
        beltCollider.isTrigger = true;
        
        beltRenderer = GetComponent<Renderer>();
        if (beltRenderer != null && beltMaterial != null) {
            beltRenderer.material = beltMaterial;
        }
        
        // Инициализация сетевой скорости
        if (Object.HasStateAuthority) {
            NetworkSpeed = conveyorSpeed;
        }
        
        // Настройка звука
        SetupAudio();
    }
    
    private void SetupAudio() {
        if (audioSource == null) {
            audioSource = GetComponent<AudioSource>();
        }
        
        if (audioSource != null && conveyorSound != null && playSound) {
            audioSource.clip = conveyorSound;
            audioSource.loop = true;
            audioSource.playOnAwake = false;
            
            if (IsActive) {
                audioSource.Play();
            }
        }
    }
    
    public override void FixedUpdateNetwork() {
        if (!Object.HasStateAuthority || !IsActive) {
            return;
        }
        
        // Применяем силу ко всем объектам на конвейере
        ApplyConveyorForce();
    }
    
    private void Update() {
        // Анимация текстуры (только локально)
        if (IsActive && beltRenderer != null) {
            AnimateTexture();
        }
        
        // Управление звуком
        UpdateAudio();
    }
    
    private void AnimateTexture() {
        if (beltRenderer.material.HasProperty(texturePropertyName)) {
            textureOffset.x += Time.deltaTime * textureScrollSpeed * NetworkSpeed / conveyorSpeed;
            beltRenderer.material.SetTextureOffset(texturePropertyName, textureOffset);
        }
    }
    
    private void UpdateAudio() {
        if (audioSource != null && playSound) {
            if (IsActive && !audioSource.isPlaying) {
                audioSource.Play();
            } else if (!IsActive && audioSource.isPlaying) {
                audioSource.Stop();
            }
            
            // Изменяем высоту звука в зависимости от скорости
            if (audioSource.isPlaying) {
                audioSource.pitch = Mathf.Lerp(0.5f, 2f, NetworkSpeed / (conveyorSpeed * 2f));
            }
        }
    }
    
    private void ApplyConveyorForce() {
        Vector3 forceDirection = transform.TransformDirection(conveyorDirection.normalized);
        
        foreach (var rb in objectsOnBelt) {
            if (rb == null || rb.isKinematic) continue;
            
            if (useVelocityMode) {
                // Режим скорости - устанавливаем целевую скорость
                Vector3 targetVelocity = forceDirection * NetworkSpeed;
                Vector3 velocityDifference = targetVelocity - Vector3.Project(rb.velocity, forceDirection);
                
                // Ограничиваем максимальную скорость
                if (rb.velocity.magnitude < maxVelocity) {
                    rb.AddForce(velocityDifference * forceMultiplier, ForceMode.Acceleration);
                }
            } else {
                // Режим силы - постоянно применяем силу
                rb.AddForce(forceDirection * NetworkSpeed * forceMultiplier, ForceMode.Force);
            }
        }
    }
    
    private void OnTriggerEnter(Collider other) {
        if (!Object.HasStateAuthority) {
            return;
        }
        
        // Проверяем слой объекта
        if ((affectedLayers.value & (1 << other.gameObject.layer)) == 0) {
            return;
        }
        
        Rigidbody rb = other.GetComponent<Rigidbody>();
        if (rb != null) {
            objectsOnBelt.Add(rb);
        }
    }
    
    private void OnTriggerExit(Collider other) {
        if (!Object.HasStateAuthority) {
            return;
        }
        
        Rigidbody rb = other.GetComponent<Rigidbody>();
        if (rb != null) {
            objectsOnBelt.Remove(rb);
        }
    }
    
    // Публичные методы для управления конвейером
    [Rpc(RpcSources.All, RpcTargets.StateAuthority)]
    public void RPC_SetActive(bool active) {
        if (Object.HasStateAuthority) {
            IsActive = active;
        }
    }
    
    [Rpc(RpcSources.All, RpcTargets.StateAuthority)]
    public void RPC_SetSpeed(float speed) {
        if (Object.HasStateAuthority) {
            NetworkSpeed = Mathf.Max(0f, speed);
        }
    }
    
    [Rpc(RpcSources.All, RpcTargets.StateAuthority)]
    public void RPC_SetDirection(Vector3 direction) {
        if (Object.HasStateAuthority) {
            conveyorDirection = direction.normalized;
        }
    }
    
    // Методы для внешнего управления
    public void SetConveyorActive(bool active) {
        if (Object.HasInputAuthority || Object.HasStateAuthority) {
            RPC_SetActive(active);
        }
    }
    
    public void SetConveyorSpeed(float speed) {
        if (Object.HasInputAuthority || Object.HasStateAuthority) {
            RPC_SetSpeed(speed);
        }
    }
    
    public void SetConveyorDirection(Vector3 direction) {
        if (Object.HasInputAuthority || Object.HasStateAuthority) {
            RPC_SetDirection(direction);
        }
    }
    
    // Вспомогательные методы
    public int GetObjectsOnBeltCount() {
        return objectsOnBelt.Count;
    }
    
    public bool IsObjectOnBelt(Rigidbody rb) {
        return objectsOnBelt.Contains(rb);
    }
    
    private void OnDrawGizmosSelected() {
        // Визуализация направления конвейера в редакторе
        Gizmos.color = Color.yellow;
        Vector3 worldDirection = transform.TransformDirection(conveyorDirection.normalized);
        Vector3 center = transform.position;
        
        Gizmos.DrawRay(center, worldDirection * 2f);
        Gizmos.DrawWireCube(center + worldDirection * 1.5f, Vector3.one * 0.2f);
    }
}
