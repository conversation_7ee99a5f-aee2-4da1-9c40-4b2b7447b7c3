using Fusion;
using UnityEngine;

/// <summary>
/// Предмет, который может взаимодействовать с конвейерными лентами
/// Наследуется от PickupItem для совместимости с существующей системой предметов
/// </summary>
[RequireComponent(typeof(Rigidbody))]
public class ConveyorItem : PickupItem {
    
    [Header("Conveyor Interaction")]
    [SerializeField] private bool canBeMovedByConveyor = true;
    [SerializeField] private float conveyorResistance = 1f; // 0 = нет сопротивления, 1 = полное сопротивление
    [SerializeField] private bool maintainRotation = false;
    [SerializeField] private bool playConveyorSound = true;
    
    [Header("Physics Override")]
    [SerializeField] private bool overridePhysics = false;
    [SerializeField] private float customMass = 1f;
    [SerializeField] private float customDrag = 0.5f;
    [SerializeField] private float customAngularDrag = 0.3f;
    
    [Header("Effects")]
    [SerializeField] private ParticleSystem movementEffect;
    [SerializeField] private AudioSource itemAudioSource;
    [SerializeField] private AudioClip conveyorMoveSound;
    
    // Сетевые переменные
    [Networked] public bool IsOnConveyor { get; set; }
    [Networked] public Vector3 ConveyorVelocity { get; set; }
    
    private Rigidbody rb;
    private ConveyorBelt currentConveyor;
    private Vector3 originalRotation;
    private bool wasKinematic;
    
    public override void Spawned() {
        base.Spawned();
        
        rb = GetComponent<Rigidbody>();
        originalRotation = transform.eulerAngles;
        wasKinematic = rb.isKinematic;
        
        // Применяем кастомные физические настройки
        if (overridePhysics) {
            ApplyCustomPhysics();
        }
        
        // Настройка аудио
        SetupAudio();
    }
    
    private void ApplyCustomPhysics() {
        if (rb != null) {
            rb.mass = customMass;
            rb.drag = customDrag;
            rb.angularDrag = customAngularDrag;
        }
    }
    
    private void SetupAudio() {
        if (itemAudioSource == null) {
            itemAudioSource = GetComponent<AudioSource>();
        }
        
        if (itemAudioSource == null && playConveyorSound) {
            itemAudioSource = gameObject.AddComponent<AudioSource>();
            itemAudioSource.playOnAwake = false;
            itemAudioSource.loop = false;
            itemAudioSource.volume = 0.3f;
        }
    }
    
    public override void FixedUpdateNetwork() {
        base.FixedUpdateNetwork();
        
        if (!Object.HasStateAuthority) return;
        
        // Обновляем состояние на конвейере
        UpdateConveyorState();
        
        // Применяем эффекты движения
        if (IsOnConveyor && canBeMovedByConveyor) {
            ApplyConveyorEffects();
        }
    }
    
    private void UpdateConveyorState() {
        // Проверяем, находится ли предмет на конвейере
        bool wasOnConveyor = IsOnConveyor;
        IsOnConveyor = currentConveyor != null && currentConveyor.IsActive;
        
        // Если состояние изменилось, обновляем эффекты
        if (wasOnConveyor != IsOnConveyor) {
            OnConveyorStateChanged(IsOnConveyor);
        }
        
        // Обновляем скорость конвейера
        if (IsOnConveyor && currentConveyor != null) {
            Vector3 conveyorDirection = currentConveyor.transform.TransformDirection(Vector3.forward);
            ConveyorVelocity = conveyorDirection * currentConveyor.NetworkSpeed;
        } else {
            ConveyorVelocity = Vector3.zero;
        }
    }
    
    private void ApplyConveyorEffects() {
        if (rb == null || rb.isKinematic) return;
        
        // Применяем сопротивление конвейеру
        if (conveyorResistance > 0f && conveyorResistance < 1f) {
            Vector3 resistanceForce = -ConveyorVelocity * conveyorResistance * rb.mass;
            rb.AddForce(resistanceForce, ForceMode.Force);
        }
        
        // Поддерживаем ротацию если нужно
        if (maintainRotation) {
            Quaternion targetRotation = Quaternion.Euler(originalRotation);
            rb.MoveRotation(Quaternion.Slerp(rb.rotation, targetRotation, Time.fixedDeltaTime * 2f));
        }
    }
    
    private void OnConveyorStateChanged(bool onConveyor) {
        // Эффекты частиц
        if (movementEffect != null) {
            if (onConveyor) {
                movementEffect.Play();
            } else {
                movementEffect.Stop();
            }
        }
        
        // Звуковые эффекты
        if (playConveyorSound && itemAudioSource != null && conveyorMoveSound != null) {
            if (onConveyor) {
                itemAudioSource.clip = conveyorMoveSound;
                itemAudioSource.Play();
            } else {
                itemAudioSource.Stop();
            }
        }
    }
    
    // Методы для взаимодействия с конвейером
    public void SetOnConveyor(ConveyorBelt conveyor) {
        currentConveyor = conveyor;
    }
    
    public void RemoveFromConveyor() {
        currentConveyor = null;
    }
    
    public void SetConveyorResistance(float resistance) {
        conveyorResistance = Mathf.Clamp01(resistance);
    }
    
    public void SetCanBeMovedByConveyor(bool canMove) {
        canBeMovedByConveyor = canMove;
    }
    
    // Переопределяем методы базового класса для учета конвейера
    public override void AttachToPlayer(PlayerController player) {
        // Убираем предмет с конвейера при подборе
        RemoveFromConveyor();
        base.AttachToPlayer(player);
    }
    
    public override void RequestDrop() {
        base.RequestDrop();
        
        // Восстанавливаем физические свойства после сброса
        if (overridePhysics && rb != null) {
            ApplyCustomPhysics();
        }
    }
    
    // Обработка коллизий для определения конвейера
    private void OnTriggerEnter(Collider other) {
        if (!Object.HasStateAuthority) return;
        
        ConveyorBelt conveyor = other.GetComponent<ConveyorBelt>();
        if (conveyor != null && canBeMovedByConveyor) {
            SetOnConveyor(conveyor);
        }
    }
    
    private void OnTriggerExit(Collider other) {
        if (!Object.HasStateAuthority) return;
        
        ConveyorBelt conveyor = other.GetComponent<ConveyorBelt>();
        if (conveyor != null && conveyor == currentConveyor) {
            RemoveFromConveyor();
        }
    }
    
    // Информационные методы
    public bool GetIsOnConveyor() {
        return IsOnConveyor;
    }
    
    public Vector3 GetConveyorVelocity() {
        return ConveyorVelocity;
    }
    
    public ConveyorBelt GetCurrentConveyor() {
        return currentConveyor;
    }
    
    public float GetConveyorResistance() {
        return conveyorResistance;
    }
    
    // Методы для отладки
    private void OnDrawGizmosSelected() {
        if (IsOnConveyor) {
            Gizmos.color = Color.cyan;
            Gizmos.DrawWireSphere(transform.position, 0.5f);
            
            if (ConveyorVelocity != Vector3.zero) {
                Gizmos.color = Color.yellow;
                Gizmos.DrawRay(transform.position, ConveyorVelocity.normalized * 2f);
            }
        }
    }
}
