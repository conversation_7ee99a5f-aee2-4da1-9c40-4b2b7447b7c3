using Fusion;
using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Контроллер для управления конвейерными лентами через UI или триггеры
/// </summary>
public class ConveyorController : NetworkBehaviour {
    
    [Header("Conveyor References")]
    [SerializeField] private ConveyorBelt[] conveyorBelts;
    
    [Header("Control Settings")]
    [SerializeField] private bool useUI = true;
    [SerializeField] private bool useTrigger = false;
    [SerializeField] private KeyCode toggleKey = KeyCode.C;
    
    [Header("UI Elements")]
    [SerializeField] private Button toggleButton;
    [SerializeField] private Slider speedSlider;
    [SerializeField] private Text statusText;
    
    [Header("Trigger Settings")]
    [SerializeField] private LayerMask triggerLayers = -1;
    [SerializeField] private bool requirePlayerToActivate = true;
    
    [Header("Timing")]
    [SerializeField] private float autoToggleInterval = 0f; // 0 = отключено
    [SerializeField] private float activeDuration = 10f;
    
    // Сетевые переменные
    [Networked] public bool IsSystemActive { get; set; } = true;
    [Networked] public float SystemSpeed { get; set; } = 5f;
    [Networked] private TickTimer AutoToggleTimer { get; set; }
    [Networked] private TickTimer ActiveTimer { get; set; }
    
    private bool localInputEnabled = true;
    
    public override void Spawned() {
        // Инициализация UI
        SetupUI();
        
        // Инициализация сетевых переменных
        if (Object.HasStateAuthority) {
            SystemSpeed = 5f;
            IsSystemActive = true;
            
            if (autoToggleInterval > 0f) {
                AutoToggleTimer = TickTimer.CreateFromSeconds(Runner, autoToggleInterval);
            }
        }
        
        // Применяем начальные настройки ко всем конвейерам
        UpdateAllConveyors();
    }
    
    private void SetupUI() {
        if (!useUI) return;
        
        if (toggleButton != null) {
            toggleButton.onClick.AddListener(() => {
                if (localInputEnabled) {
                    ToggleConveyors();
                }
            });
        }
        
        if (speedSlider != null) {
            speedSlider.onValueChanged.AddListener((value) => {
                if (localInputEnabled) {
                    SetSpeed(value);
                }
            });
            speedSlider.value = SystemSpeed;
        }
    }
    
    public override void FixedUpdateNetwork() {
        if (!Object.HasStateAuthority) return;
        
        // Автоматическое переключение
        if (autoToggleInterval > 0f && AutoToggleTimer.Expired(Runner)) {
            IsSystemActive = !IsSystemActive;
            AutoToggleTimer = TickTimer.CreateFromSeconds(Runner, autoToggleInterval);
            
            if (IsSystemActive && activeDuration > 0f) {
                ActiveTimer = TickTimer.CreateFromSeconds(Runner, activeDuration);
            }
        }
        
        // Автоматическое отключение после активного периода
        if (IsSystemActive && activeDuration > 0f && ActiveTimer.Expired(Runner)) {
            IsSystemActive = false;
        }
    }
    
    private void Update() {
        // Управление с клавиатуры
        if (localInputEnabled && Input.GetKeyDown(toggleKey)) {
            ToggleConveyors();
        }
        
        // Обновление UI
        UpdateUI();
    }
    
    private void UpdateUI() {
        if (!useUI) return;
        
        if (statusText != null) {
            statusText.text = $"Конвейер: {(IsSystemActive ? "ВКЛ" : "ВЫКЛ")} | Скорость: {SystemSpeed:F1}";
        }
        
        if (toggleButton != null) {
            var colors = toggleButton.colors;
            colors.normalColor = IsSystemActive ? Color.green : Color.red;
            toggleButton.colors = colors;
        }
        
        if (speedSlider != null && Mathf.Abs(speedSlider.value - SystemSpeed) > 0.1f) {
            speedSlider.value = SystemSpeed;
        }
    }
    
    private void UpdateAllConveyors() {
        if (conveyorBelts == null) return;
        
        foreach (var conveyor in conveyorBelts) {
            if (conveyor != null) {
                conveyor.SetConveyorActive(IsSystemActive);
                conveyor.SetConveyorSpeed(SystemSpeed);
            }
        }
    }
    
    // Публичные методы управления
    public void ToggleConveyors() {
        if (Object.HasInputAuthority || Object.HasStateAuthority) {
            RPC_ToggleSystem();
        }
    }
    
    public void SetSpeed(float speed) {
        if (Object.HasInputAuthority || Object.HasStateAuthority) {
            RPC_SetSystemSpeed(speed);
        }
    }
    
    public void ActivateConveyors() {
        if (Object.HasInputAuthority || Object.HasStateAuthority) {
            RPC_SetSystemActive(true);
        }
    }
    
    public void DeactivateConveyors() {
        if (Object.HasInputAuthority || Object.HasStateAuthority) {
            RPC_SetSystemActive(false);
        }
    }
    
    // RPC методы
    [Rpc(RpcSources.All, RpcTargets.StateAuthority)]
    private void RPC_ToggleSystem() {
        if (Object.HasStateAuthority) {
            IsSystemActive = !IsSystemActive;
            UpdateAllConveyors();
        }
    }
    
    [Rpc(RpcSources.All, RpcTargets.StateAuthority)]
    private void RPC_SetSystemActive(bool active) {
        if (Object.HasStateAuthority) {
            IsSystemActive = active;
            UpdateAllConveyors();
        }
    }
    
    [Rpc(RpcSources.All, RpcTargets.StateAuthority)]
    private void RPC_SetSystemSpeed(float speed) {
        if (Object.HasStateAuthority) {
            SystemSpeed = Mathf.Clamp(speed, 0f, 20f);
            UpdateAllConveyors();
        }
    }
    
    // Триггер для активации игроками
    private void OnTriggerEnter(Collider other) {
        if (!useTrigger || !Object.HasStateAuthority) {
            return;
        }
        
        if ((triggerLayers.value & (1 << other.gameObject.layer)) == 0) {
            return;
        }
        
        if (requirePlayerToActivate) {
            PlayerController player = other.GetComponentInParent<PlayerController>();
            if (player == null) {
                return;
            }
        }
        
        // Активируем конвейеры при входе в триггер
        IsSystemActive = true;
        UpdateAllConveyors();
        
        // Устанавливаем таймер активности если настроен
        if (activeDuration > 0f) {
            ActiveTimer = TickTimer.CreateFromSeconds(Runner, activeDuration);
        }
    }
    
    // Методы для внешнего управления
    public void AddConveyor(ConveyorBelt conveyor) {
        if (conveyor == null) return;
        
        var newArray = new ConveyorBelt[conveyorBelts.Length + 1];
        conveyorBelts.CopyTo(newArray, 0);
        newArray[conveyorBelts.Length] = conveyor;
        conveyorBelts = newArray;
        
        // Применяем текущие настройки к новому конвейеру
        conveyor.SetConveyorActive(IsSystemActive);
        conveyor.SetConveyorSpeed(SystemSpeed);
    }
    
    public void RemoveConveyor(ConveyorBelt conveyor) {
        if (conveyor == null || conveyorBelts == null) return;
        
        var list = new System.Collections.Generic.List<ConveyorBelt>(conveyorBelts);
        list.Remove(conveyor);
        conveyorBelts = list.ToArray();
    }
    
    public void SetInputEnabled(bool enabled) {
        localInputEnabled = enabled;
    }
    
    // Информационные методы
    public int GetActiveConveyorCount() {
        if (conveyorBelts == null) return 0;
        
        int count = 0;
        foreach (var conveyor in conveyorBelts) {
            if (conveyor != null && conveyor.IsActive) {
                count++;
            }
        }
        return count;
    }
    
    public int GetTotalObjectsOnBelts() {
        if (conveyorBelts == null) return 0;
        
        int total = 0;
        foreach (var conveyor in conveyorBelts) {
            if (conveyor != null) {
                total += conveyor.GetObjectsOnBeltCount();
            }
        }
        return total;
    }
}
